import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  Button,
  Grid,
  TextField,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Email as EmailIcon,
  FileCopy as DuplicateIcon,
  Receipt as ReceiptIcon,
  Description as DescriptionIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { StandardDatePicker } from '../../../shared/components';
import { formatCurrency } from '../../../shared/utils/formatters';
import StatCard from '../../../shared/components/StatCard';
import dayjs from 'dayjs';

// Mock data interfaces (will be replaced with actual service calls)
interface VendorBill {
  id: number;
  bill_number: string;
  vendor_name: string;
  bill_date: string;
  due_date: string;
  status: 'draft' | 'approved' | 'paid' | 'rejected';
  total_amount: number;
  amount_paid: number;
  balance_due: number;
  reference_number?: string;
  source_type?: 'manual' | 'grn' | 'po' | 'return_note';
}

interface VendorBillStats {
  total_bills: number;
  total_payables: number;
  outstanding_amount: number;
  overdue_count: number;
  draft_count: number;
  paid_count: number;
}

interface VendorBillFilters {
  status?: string;
  vendor?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
  source_type?: string;
}

const VendorBillsPage: React.FC = () => {
  const navigate = useNavigate();
  
  // State
  const [bills, setBills] = useState<VendorBill[]>([]);
  const [stats, setStats] = useState<VendorBillStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<VendorBillFilters>({});

  // Mock data (will be replaced with API calls)
  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setStats({
        total_bills: 45,
        total_payables: 125000.00,
        outstanding_amount: 85000.00,
        overdue_count: 8,
        draft_count: 12,
        paid_count: 25,
      });
      
      setBills([
        {
          id: 1,
          bill_number: 'VBILL-2024-001',
          vendor_name: 'ABC Suppliers Ltd',
          bill_date: '2024-01-15',
          due_date: '2024-02-14',
          status: 'approved',
          total_amount: 15000.00,
          amount_paid: 0.00,
          balance_due: 15000.00,
          reference_number: 'INV-2024-001',
          source_type: 'grn',
        },
        {
          id: 2,
          bill_number: 'VBILL-2024-002',
          vendor_name: 'XYZ Services Inc',
          bill_date: '2024-01-20',
          due_date: '2024-02-19',
          status: 'paid',
          total_amount: 8500.00,
          amount_paid: 8500.00,
          balance_due: 0.00,
          reference_number: 'SRV-2024-005',
          source_type: 'po',
        },
      ]);
      
      setLoading(false);
    }, 1000);
  }, [filters]);

  const handleFilterChange = (field: keyof VendorBillFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [field]: value || undefined
    }));
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this vendor bill?')) {
      // TODO: Implement delete functionality
      console.log('Delete bill:', id);
    }
  };

  const handleDuplicate = async (id: number) => {
    // TODO: Implement duplicate functionality
    console.log('Duplicate bill:', id);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'success';
      case 'approved': return 'info';
      case 'draft': return 'default';
      case 'rejected': return 'error';
      default: return 'default';
    }
  };

  const getSourceIcon = (sourceType?: string) => {
    switch (sourceType) {
      case 'grn': return <ReceiptIcon fontSize="small" />;
      case 'po': return <DescriptionIcon fontSize="small" />;
      case 'return_note': return <AssignmentIcon fontSize="small" />;
      default: return <EditIcon fontSize="small" />;
    }
  };

  const getSourceLabel = (sourceType?: string) => {
    switch (sourceType) {
      case 'grn': return 'From GRN';
      case 'po': return 'From PO';
      case 'return_note': return 'From Return';
      default: return 'Manual';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
            Vendor Bills
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
            Manage accounts payable and vendor bills
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<ReceiptIcon />}
            onClick={() => navigate('/dashboard/purchases/vendor-bills/create-from-grn')}
            sx={{ borderRadius: '8px' }}
          >
            From GRN
          </Button>
          <Button
            variant="outlined"
            startIcon={<DescriptionIcon />}
            onClick={() => navigate('/dashboard/purchases/vendor-bills/create-from-po')}
            sx={{ borderRadius: '8px' }}
          >
            From PO
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/dashboard/purchases/vendor-bills/create')}
            sx={{ 
              borderRadius: '8px',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              }
            }}
          >
            Create Bill
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
              color: 'white',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mx: 'auto', mb: 1 }}>
                  <ReceiptIcon />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {stats.total_bills}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Total Bills
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', 
              color: 'white',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {formatCurrency(stats.total_payables)}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Total Payables
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', 
              color: 'white',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {formatCurrency(stats.outstanding_amount)}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Outstanding
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', 
              color: 'white',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {stats.overdue_count}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Overdue
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', 
              color: '#333',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {stats.paid_count}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Paid
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)', 
              color: '#333',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {stats.draft_count}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Draft
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3, borderRadius: '12px', boxShadow: '0 2px 12px rgba(0,0,0,0.08)' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ mb: 2, fontWeight: 600 }}>
            Filters
          </Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                size="small"
                label="Search"
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Search bills..."
                sx={{ borderRadius: '8px' }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                size="small"
                select
                label="Status"
                value={filters.status || ''}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <MenuItem value="">All Status</MenuItem>
                <MenuItem value="draft">Draft</MenuItem>
                <MenuItem value="approved">Approved</MenuItem>
                <MenuItem value="paid">Paid</MenuItem>
                <MenuItem value="rejected">Rejected</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                size="small"
                select
                label="Source"
                value={filters.source_type || ''}
                onChange={(e) => handleFilterChange('source_type', e.target.value)}
              >
                <MenuItem value="">All Sources</MenuItem>
                <MenuItem value="manual">Manual</MenuItem>
                <MenuItem value="grn">From GRN</MenuItem>
                <MenuItem value="po">From PO</MenuItem>
                <MenuItem value="return_note">From Return</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} md={2}>
              <StandardDatePicker
                label="From Date"
                value={filters.date_from ? dayjs(filters.date_from) : null}
                onChange={(date) => handleFilterChange('date_from', date?.format('YYYY-MM-DD'))}
                businessContext="general"
                dateFormat="DD/MM/YYYY"
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <StandardDatePicker
                label="To Date"
                value={filters.date_to ? dayjs(filters.date_to) : null}
                onChange={(date) => handleFilterChange('date_to', date?.format('YYYY-MM-DD'))}
                businessContext="general"
                dateFormat="DD/MM/YYYY"
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                onClick={() => setFilters({})}
                fullWidth
                sx={{ borderRadius: '8px' }}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Bills Table */}
      <Card sx={{ borderRadius: '12px', boxShadow: '0 2px 12px rgba(0,0,0,0.08)' }}>
        <CardContent sx={{ p: 0 }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Bill Number</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Vendor</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Date</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Due Date</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Status</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Source</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 600, color: '#495057' }}>Amount</TableCell>
                  <TableCell align="center" sx={{ fontWeight: 600, color: '#495057' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {bills.map((bill) => (
                  <TableRow key={bill.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium" color="primary">
                        {bill.bill_number}
                      </Typography>
                      {bill.reference_number && (
                        <Typography variant="caption" color="text.secondary">
                          Ref: {bill.reference_number}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {bill.vendor_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {dayjs(bill.bill_date).format('DD/MM/YYYY')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {dayjs(bill.due_date).format('DD/MM/YYYY')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={bill.status.toUpperCase()}
                        color={getStatusColor(bill.status) as any}
                        size="small"
                        sx={{ borderRadius: '6px' }}
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getSourceIcon(bill.source_type)}
                        <Typography variant="caption">
                          {getSourceLabel(bill.source_type)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight="medium">
                        {formatCurrency(bill.total_amount)}
                      </Typography>
                      {bill.balance_due > 0 && (
                        <Typography variant="caption" color="error">
                          Due: {formatCurrency(bill.balance_due)}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <Tooltip title="View">
                          <IconButton
                            size="small"
                            onClick={() => navigate(`/dashboard/purchases/vendor-bills/${bill.id}`)}
                          >
                            <ViewIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit">
                          <IconButton
                            size="small"
                            onClick={() => navigate(`/dashboard/purchases/vendor-bills/${bill.id}/edit`)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Duplicate">
                          <IconButton
                            size="small"
                            onClick={() => handleDuplicate(bill.id)}
                          >
                            <DuplicateIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete">
                          <IconButton
                            size="small"
                            onClick={() => handleDelete(bill.id)}
                            color="error"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default VendorBillsPage;

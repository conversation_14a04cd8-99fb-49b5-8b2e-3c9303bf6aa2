import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Autocomplete,
  IconButton,
  Box,
  Typography,
  Tooltip,
  MenuItem,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import FormattedCurrencyInput from './FormattedCurrencyInput';
import { formatCurrency } from '../utils/formatters';
import { loadChartOfAccountsFast } from '../../services/gl.service';
import type { Account } from '../types/gl.types';
import { salesTaxService, type SalesTaxOption } from '../../services/sales-tax.service';

// Vendor Bill Line Item interface
export interface VendorBillLineItem {
  id: string;
  product_id?: number | null;
  product_name?: string;
  item_description: string;
  quantity: number;
  unit_price: number;
  line_total: number;
  tax_rate?: number;
  tax_amount?: number;
  account_code?: string;
}

// Product option interface
export interface ProductOption {
  id: number;
  name: string;
  description?: string;
  cost_price?: number;
  expense_account_code?: string;
}



export interface VendorBillLineTableProps {
  lines: VendorBillLineItem[];
  products?: ProductOption[];
  salesTaxes?: SalesTaxOption[];
  onLineChange: (lineId: string, field: string, value: any) => void;
  onAddLine: () => void;
  onRemoveLine: (lineId: string) => void;
  currencySymbol?: string;
  readOnly?: boolean;
  minLines?: number;
  tableHeight?: string;
  showTaxColumn?: boolean;
  showAccountColumn?: boolean;
}

const VendorBillLineTable: React.FC<VendorBillLineTableProps> = ({
  lines,
  products = [],
  salesTaxes = [],
  onLineChange,
  onAddLine,
  onRemoveLine,
  currencySymbol = '$',
  readOnly = false,
  minLines = 4,
  tableHeight = '400px',
  showTaxColumn = true,
  showAccountColumn = true,
}) => {
  const [columnWidths, setColumnWidths] = useState({
    product: 180,
    description: 250,
    quantity: 80,
    unitPrice: 100,
    taxRate: 80,
    amount: 100,
    account: 140,
    actions: 60,
  });

  // COA accounts state
  const [expenseAssetAccounts, setExpenseAssetAccounts] = useState<Account[]>([]);
  const [accountsLoading, setAccountsLoading] = useState(true);

  // Products state
  const [allProducts, setAllProducts] = useState<ProductOption[]>([]);
  const [productsLoading, setProductsLoading] = useState(true);

  // Sales tax state
  const [inputTaxes, setInputTaxes] = useState<SalesTaxOption[]>([]);
  const [taxesLoading, setTaxesLoading] = useState(true);

  // Load expense and asset accounts from COA
  useEffect(() => {
    const loadExpenseAssetAccounts = async () => {
      try {
        setAccountsLoading(true);
        console.log('Loading expense and asset accounts from COA...');

        const coaData = await loadChartOfAccountsFast();
        console.log('Raw COA response for line items:', coaData);

        // Filter for expense and asset accounts (debit accounts for vendor bills)
        const expenseAssetAccounts = coaData.accounts?.filter((account: Account) =>
          (account.account_type_name === 'Expenses' || account.account_type_name === 'Assets') &&
          account.is_active &&
          !account.is_header_account
        ) || [];

        console.log('Filtered expense/asset accounts:', expenseAssetAccounts);
        console.log('Number of expense/asset accounts loaded:', expenseAssetAccounts.length);
        setExpenseAssetAccounts(expenseAssetAccounts);

      } catch (error) {
        console.error('Failed to load expense/asset accounts:', error);
      } finally {
        setAccountsLoading(false);
      }
    };

    loadExpenseAssetAccounts();

    // Load products and services
    const loadProducts = async () => {
      try {
        setProductsLoading(true);
        console.log('Loading products and services...');

        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        // Try multiple endpoints for products
        const endpoints = [
          { url: 'http://localhost:8000/api/sales/products/', name: 'sales' },
          { url: 'http://localhost:8000/api/gl/products/', name: 'gl' },
          { url: 'http://localhost:8000/api/pricing/products/', name: 'pricing' }
        ];

        for (const endpoint of endpoints) {
          try {
            console.log(`Trying ${endpoint.name} products endpoint:`, endpoint.url);

            const response = await fetch(`${endpoint.url}?page_size=100`, {
              headers: {
                'Authorization': `Token ${token}`,
                'Content-Type': 'application/json',
              },
            });

            if (response.ok) {
              const data = await response.json();
              console.log(`Raw ${endpoint.name} products response:`, data);

              // Handle both paginated and direct array responses
              let productsData = [];
              if (data && typeof data === 'object' && 'results' in data) {
                productsData = data.results || [];
              } else if (Array.isArray(data)) {
                productsData = data;
              }

              // Transform to ProductOption format
              const transformedProducts: ProductOption[] = productsData.map((product: any) => ({
                id: product.id,
                name: product.name || product.product_name || product.display_name,
                description: product.description || '',
                cost_price: product.cost_price || product.unit_price || 0,
                expense_account_code: product.expense_account_code || product.account_code || ''
              }));

              console.log(`Transformed ${endpoint.name} products:`, transformedProducts);
              console.log(`Number of products loaded from ${endpoint.name}:`, transformedProducts.length);

              // Combine with existing products and passed products prop
              const combinedProducts = [...products, ...transformedProducts];
              setAllProducts(combinedProducts);

              if (transformedProducts.length > 0) {
                break; // Success, stop trying other endpoints
              }
            } else {
              console.error(`${endpoint.name} products API error:`, response.status);
            }
          } catch (error) {
            console.error(`Error with ${endpoint.name} products endpoint:`, error);
          }
        }

      } catch (error) {
        console.error('Failed to load products:', error);
      } finally {
        setProductsLoading(false);
      }
    };

    loadProducts();

    // Load input taxes (for vendor bills)
    const loadInputTaxes = async () => {
      try {
        setTaxesLoading(true);
        console.log('Loading input taxes from company setup...');

        const inputTaxes = await salesTaxService.getInputTaxes();
        console.log('Raw input taxes response:', inputTaxes);
        console.log('Number of input taxes loaded:', inputTaxes.length);

        setInputTaxes(inputTaxes);

        if (inputTaxes.length === 0) {
          console.warn('No input taxes found in company setup');
        }

      } catch (error) {
        console.error('Failed to load input taxes:', error);
        // Set default input tax options if API fails
        setInputTaxes([
          { id: 0, tax_type: 'input', description: 'No Tax', rate: 0 },
          { id: 1, tax_type: 'input', description: 'Standard Input Tax', rate: 10 },
          { id: 2, tax_type: 'input', description: 'Reduced Input Tax', rate: 5 },
        ]);
      } finally {
        setTaxesLoading(false);
      }
    };

    loadInputTaxes();
  }, [products]);

  // Calculate line total when quantity or unit price changes
  const calculateLineTotal = (quantity: number, unitPrice: number) => {
    return quantity * unitPrice;
  };

  // Calculate tax amount
  const calculateTaxAmount = (lineTotal: number, taxRate: number) => {
    return lineTotal * (taxRate / 100);
  };

  // Handle product selection
  const handleProductSelect = (lineId: string, product: ProductOption | null) => {
    if (product) {
      onLineChange(lineId, 'product_id', product.id);
      onLineChange(lineId, 'product_name', product.name);
      onLineChange(lineId, 'item_description', product.description || product.name);
      onLineChange(lineId, 'unit_price', product.cost_price || 0);
      onLineChange(lineId, 'account_code', product.expense_account_code || '5010-COGS');
      
      // Recalculate line total
      const line = lines.find(l => l.id === lineId);
      if (line) {
        const newLineTotal = calculateLineTotal(line.quantity, product.cost_price || 0);
        onLineChange(lineId, 'line_total', newLineTotal);
        
        // Recalculate tax if tax rate exists
        if (line.tax_rate) {
          const newTaxAmount = calculateTaxAmount(newLineTotal, line.tax_rate);
          onLineChange(lineId, 'tax_amount', newTaxAmount);
        }
      }
    } else {
      onLineChange(lineId, 'product_id', null);
      onLineChange(lineId, 'product_name', '');
    }
  };

  // Handle quantity change
  const handleQuantityChange = (lineId: string, quantity: number) => {
    onLineChange(lineId, 'quantity', quantity);
    
    const line = lines.find(l => l.id === lineId);
    if (line) {
      const newLineTotal = calculateLineTotal(quantity, line.unit_price);
      onLineChange(lineId, 'line_total', newLineTotal);
      
      if (line.tax_rate) {
        const newTaxAmount = calculateTaxAmount(newLineTotal, line.tax_rate);
        onLineChange(lineId, 'tax_amount', newTaxAmount);
      }
    }
  };

  // Handle unit price change
  const handleUnitPriceChange = (lineId: string, unitPrice: number) => {
    onLineChange(lineId, 'unit_price', unitPrice);
    
    const line = lines.find(l => l.id === lineId);
    if (line) {
      const newLineTotal = calculateLineTotal(line.quantity, unitPrice);
      onLineChange(lineId, 'line_total', newLineTotal);
      
      if (line.tax_rate) {
        const newTaxAmount = calculateTaxAmount(newLineTotal, line.tax_rate);
        onLineChange(lineId, 'tax_amount', newTaxAmount);
      }
    }
  };

  // Handle tax rate change
  const handleTaxRateChange = (lineId: string, taxRate: number) => {
    onLineChange(lineId, 'tax_rate', taxRate);

    const line = lines.find(l => l.id === lineId);
    if (line) {
      const newTaxAmount = calculateTaxAmount(line.line_total, taxRate);
      onLineChange(lineId, 'tax_amount', newTaxAmount);
    }
  };

  return (
    <Box sx={{ width: '100%', overflow: 'hidden' }}>
      <TableContainer
        component={Paper}
        sx={{
          maxHeight: tableHeight,
          border: '1px solid #dee2e6',
          borderRadius: '8px',
          '& .MuiTableCell-root': {
            borderRight: '1px solid #dee2e6',
            padding: '6px 8px',
          },
          '& .MuiTableCell-head': {
            backgroundColor: '#f8f9fa',
            fontWeight: 600,
            fontSize: '0.875rem',
            color: '#495057',
            borderBottom: '2px solid #dee2e6',
          },
        }}
      >
        <Table stickyHeader size="small" sx={{ tableLayout: 'fixed' }}>
          <TableHead>
            <TableRow>
              <TableCell sx={{ width: columnWidths.product, minWidth: 160 }}>
                Product/Service
              </TableCell>
              <TableCell sx={{ width: columnWidths.description, minWidth: 200 }}>
                Description
              </TableCell>
              <TableCell align="center" sx={{ width: columnWidths.quantity, minWidth: 70 }}>
                Qty
              </TableCell>
              <TableCell align="right" sx={{ width: columnWidths.unitPrice, minWidth: 90 }}>
                Rate
              </TableCell>
              {showAccountColumn && (
                <TableCell sx={{ width: columnWidths.account, minWidth: 120 }}>
                  Account
                </TableCell>
              )}
              {showTaxColumn && (
                <TableCell align="center" sx={{ width: columnWidths.taxRate, minWidth: 70 }}>
                  Tax %
                </TableCell>
              )}
              <TableCell align="right" sx={{ width: columnWidths.amount, minWidth: 90 }}>
                Amount
              </TableCell>
              {!readOnly && (
                <TableCell align="center" sx={{ width: columnWidths.actions, minWidth: 50 }}>
                  <Tooltip title="Add Line">
                    <IconButton size="small" onClick={onAddLine} color="primary">
                      <AddIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {lines.map((line) => (
              <TableRow key={line.id} hover>
                {/* Product/Service Column */}
                <TableCell>
                  <Autocomplete
                    size="small"
                    disabled={readOnly}
                    loading={productsLoading}
                    options={allProducts}
                    getOptionLabel={(option) => option.name}
                    value={allProducts.find(p => p.id === line.product_id) || null}
                    onChange={(_, newValue) => handleProductSelect(line.id, newValue)}
                    noOptionsText={productsLoading ? "Loading products..." : "No products found"}
                    renderOption={(props, option) => (
                      <Box component="li" {...props}>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
                            {option.name}
                          </Typography>
                          {option.description && (
                            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                              {option.description}
                            </Typography>
                          )}
                          {option.cost_price != null && option.cost_price > 0 && (
                            <Typography variant="caption" color="primary" sx={{ fontSize: '0.75rem', ml: 1 }}>
                              Cost: {formatCurrency(option.cost_price)}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Select product..."
                        variant="outlined"
                        size="small"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            minHeight: '36px',
                          },
                        }}
                      />
                    )}
                  />
                </TableCell>

                {/* Description Column */}
                <TableCell>
                  <TextField
                    fullWidth
                    size="small"
                    disabled={readOnly}
                    value={line.item_description}
                    onChange={(e) => onLineChange(line.id, 'item_description', e.target.value)}
                    placeholder="Enter description..."
                    multiline
                    maxRows={2}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        minHeight: '36px',
                      },
                    }}
                  />
                </TableCell>

                {/* Quantity Column */}
                <TableCell>
                  <TextField
                    fullWidth
                    size="small"
                    type="number"
                    disabled={readOnly}
                    value={line.quantity}
                    onChange={(e) => handleQuantityChange(line.id, parseFloat(e.target.value) || 0)}
                    inputProps={{ min: 0, step: 0.01 }}
                    sx={{
                      '& .MuiOutlinedInput-input': {
                        textAlign: 'center',
                      },
                    }}
                  />
                </TableCell>

                {/* Unit Price Column */}
                <TableCell>
                  <FormattedCurrencyInput
                    fullWidth
                    size="small"
                    disabled={readOnly}
                    name={`unit_price_${line.id}`}
                    value={line.unit_price}
                    onChange={(e) => handleUnitPriceChange(line.id, parseFloat(e.target.value) || 0)}
                    currencySymbol={currencySymbol}
                  />
                </TableCell>

                {/* Account Code Column */}
                {showAccountColumn && (
                  <TableCell>
                    <TextField
                      fullWidth
                      select
                      size="small"
                      disabled={readOnly || accountsLoading}
                      value={line.account_code || ''}
                      onChange={(e) => onLineChange(line.id, 'account_code', e.target.value)}
                      placeholder="Select account..."
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          minHeight: '36px',
                        },
                      }}
                    >
                      {accountsLoading ? (
                        <MenuItem disabled>
                          <em>Loading accounts...</em>
                        </MenuItem>
                      ) : expenseAssetAccounts.length === 0 ? (
                        <MenuItem disabled>
                          <em>No accounts found</em>
                        </MenuItem>
                      ) : (
                        expenseAssetAccounts.map((account) => (
                          <MenuItem key={account.id} value={account.account_number}>
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
                                {account.account_number} - {account.account_name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                                {account.account_type_name}
                              </Typography>
                            </Box>
                          </MenuItem>
                        ))
                      )}
                    </TextField>
                  </TableCell>
                )}

                {/* Tax Rate Column */}
                {showTaxColumn && (
                  <TableCell>
                    <TextField
                      fullWidth
                      select
                      size="small"
                      disabled={readOnly || taxesLoading}
                      value={line.tax_rate !== undefined ? Number(line.tax_rate) : ''}
                      onChange={(e) => handleTaxRateChange(line.id, parseFloat(e.target.value) || 0)}
                      placeholder="Select tax rate..."
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          minHeight: '36px',
                        },
                      }}
                    >
                      {taxesLoading ? (
                        <MenuItem disabled>
                          <em>Loading taxes...</em>
                        </MenuItem>
                      ) : inputTaxes.length === 0 ? (
                        <MenuItem disabled>
                          <em>No input taxes found</em>
                        </MenuItem>
                      ) : (
                        [
                          // Add a "No Tax" option
                          <MenuItem key="no-tax" value={0}>
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
                                0% - No Tax
                              </Typography>
                            </Box>
                          </MenuItem>,
                          ...inputTaxes.map((tax) => (
                            <MenuItem key={tax.id} value={Number(tax.rate)}>
                              <Box>
                                <Typography variant="body2" sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
                                  {tax.rate}% - {tax.description}
                                </Typography>
                                {tax.remarks && (
                                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                                    {tax.remarks}
                                  </Typography>
                                )}
                              </Box>
                            </MenuItem>
                          ))
                        ]
                      )}
                    </TextField>
                  </TableCell>
                )}

                {/* Amount Column */}
                <TableCell>
                  <Typography
                    variant="body2"
                    sx={{
                      textAlign: 'right',
                      fontWeight: 500,
                      padding: '8px 12px',
                      backgroundColor: '#f8f9fa',
                      borderRadius: '4px',
                      minHeight: '36px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'flex-end',
                    }}
                  >
                    {formatCurrency(line.line_total)}
                  </Typography>
                </TableCell>

                {/* Actions Column */}
                {!readOnly && (
                  <TableCell align="center">
                    <Tooltip title="Remove Line">
                      <span>
                        <IconButton
                          size="small"
                          onClick={() => onRemoveLine(line.id)}
                          disabled={lines.length <= minLines}
                          color="error"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </span>
                    </Tooltip>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default VendorBillLineTable;

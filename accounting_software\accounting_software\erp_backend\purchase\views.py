from django.shortcuts import render
from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum, Count
from django.utils import timezone
from datetime import datetime, timedelta

from .models import PurchaseOrder, PurchaseOrderLineItem, VendorBill, VendorBillItem
from contacts.models import Contact, Vendor
from .serializers import VendorSerializer, PurchaseOrderSerializer, VendorBillSerializer
from .pagination import DropdownPagination
# Import PaymentTerm from sales module since it's shared
from sales.models import PaymentTerm
from sales.serializers import PaymentTermSerializer


class VendorViewSet(viewsets.ModelViewSet):
    """ViewSet for managing vendors"""
    queryset = Vendor.objects.select_related('contact').all()
    serializer_class = VendorSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['preferred_vendor', 'vendor_category']
    search_fields = ['contact__name', 'company_name', 'contact__email', 'contact__phone', 'vendor_code']
    ordering_fields = ['contact__name', 'contact__created_at', 'credit_limit']
    ordering = ['contact__name']

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by credit limit
        min_credit_limit = self.request.query_params.get('min_credit_limit')
        max_credit_limit = self.request.query_params.get('max_credit_limit')
        
        if min_credit_limit is not None:
            queryset = queryset.filter(credit_limit__gte=min_credit_limit)
        if max_credit_limit is not None:
            queryset = queryset.filter(credit_limit__lte=max_credit_limit)
        
        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get vendor statistics"""
        total_vendors = self.get_queryset().count()
        preferred_vendors = self.get_queryset().filter(preferred_vendor=True).count()
        
        # Calculate total credit limits available
        total_credit_limit = self.get_queryset().aggregate(
            total=Sum('credit_limit')
        )['total'] or 0
        
        # Get recent vendors (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        new_vendors = self.get_queryset().filter(
            contact__created_at__gte=thirty_days_ago
        ).count()
        
        # Get vendors by category
        vendors_by_category = self.get_queryset().values('vendor_category').annotate(
            count=Count('vendor_category')
        ).order_by('-count')
        
        return Response({
            'total_vendors': total_vendors,
            'preferred_vendors': preferred_vendors,
            'total_credit_limit': float(total_credit_limit),
            'new_vendors_30_days': new_vendors,
            'vendors_by_category': list(vendors_by_category),
        })

    @action(detail=False, methods=['get'], pagination_class=DropdownPagination)
    def dropdown(self, request):
        """Get all vendors for dropdown (no pagination)"""
        queryset = self.get_queryset().order_by('contact__name')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class PurchaseOrderViewSet(viewsets.ModelViewSet):
    """ViewSet for managing purchase orders"""
    queryset = PurchaseOrder.objects.select_related('vendor').prefetch_related('line_items')
    serializer_class = PurchaseOrderSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'vendor', 'po_date']
    search_fields = ['po_number', 'vendor__name', 'reference_number']
    ordering_fields = ['po_number', 'po_date', 'total_amount', 'created_at']
    ordering = ['-po_date', '-created_at']

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(po_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(po_date__lte=end_date)
        
        # Filter by amount range
        min_amount = self.request.query_params.get('min_amount')
        max_amount = self.request.query_params.get('max_amount')
        
        if min_amount is not None:
            queryset = queryset.filter(total_amount__gte=min_amount)
        if max_amount is not None:
            queryset = queryset.filter(total_amount__lte=max_amount)
        
        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get purchase order statistics"""
        queryset = self.get_queryset()
        
        total_pos = queryset.count()
        draft_pos = queryset.filter(status='draft').count()
        pending_pos = queryset.filter(status='pending').count()
        sent_pos = queryset.filter(status='sent').count()
        received_pos = queryset.filter(status='received').count()
        
        # Calculate total amounts
        total_amount = queryset.aggregate(total=Sum('total_amount'))['total'] or 0
        pending_amount = queryset.filter(status__in=['pending', 'sent']).aggregate(
            total=Sum('balance_due')
        )['total'] or 0
        
        # Get recent POs (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_pos = queryset.filter(created_at__gte=thirty_days_ago).count()
        
        return Response({
            'total_purchase_orders': total_pos,
            'draft_pos': draft_pos,
            'pending_pos': pending_pos,
            'sent_pos': sent_pos,
            'received_pos': received_pos,
            'total_amount': float(total_amount),
            'pending_amount': float(pending_amount),
            'recent_pos_30_days': recent_pos,
        })

    @action(detail=True, methods=['post'])
    def send(self, request, pk=None):
        """Mark purchase order as sent"""
        purchase_order = self.get_object()
        if purchase_order.status == 'draft':
            purchase_order.status = 'sent'
            purchase_order.save()
            return Response({'status': 'Purchase order sent'})
        return Response(
            {'error': 'Purchase order cannot be sent from current status'}, 
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=True, methods=['post'])
    def receive(self, request, pk=None):
        """Mark purchase order as received"""
        purchase_order = self.get_object()
        if purchase_order.status in ['sent', 'acknowledged', 'partial']:
            purchase_order.status = 'received'
            purchase_order.amount_received = purchase_order.total_amount
            purchase_order.save()
            return Response({'status': 'Purchase order marked as received'})
        return Response(
            {'error': 'Purchase order cannot be received from current status'}, 
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=False, methods=['get'])
    def billable_receipts(self, request):
        """
        Get all GRNs and GRN Returns that are ready for billing
        
        Returns:
        - Posted GRNs that don't have bills yet
        - Posted GRN Returns that don't have credit notes yet
        - With PO details and vendor information
        """
        try:
            from inventory.models import GoodsReceiptNote, GoodsReturnNote
            from gl.models import JournalEntry
            from django.db.models import Sum, Count, Q
            
            # Helper function to safely extract vendor information
            def get_vendor_info(vendor_contact):
                """Safely extract vendor information from Contact"""
                if not vendor_contact:
                    return {
                        'vendor_id': None,
                        'vendor_name': 'Unknown Vendor',
                        'vendor_email': '',
                        'vendor_payment_terms': 'net30'
                    }
                
                # For new structure: vendor_contact is a Contact object
                return {
                    'vendor_id': vendor_contact.id,
                    'vendor_name': vendor_contact.name,
                    'vendor_email': vendor_contact.email,
                    'vendor_payment_terms': getattr(vendor_contact, 'payment_terms', 'net30')
                }

            # Get GRN document numbers that already have journal entries (already converted to bills)
            grn_document_numbers_with_journal_entries = set(
                JournalEntry.objects.filter(
                    source_document_type='GRN'
                ).exclude(
                    source_document_id__startswith='RET-'  # Exclude GRN Returns
                ).values_list('source_document_id', flat=True)
            )
            
            # Get GRN Return document numbers that already have journal entries (already converted to credit notes)
            grn_return_document_numbers_with_journal_entries = set(
                JournalEntry.objects.filter(
                    source_document_type='RET'
                ).values_list('source_document_id', flat=True)
            )
            
            # Get billable GRNs (posted but not billed)
            billable_grns = GoodsReceiptNote.objects.filter(
                status='POSTED'
            ).exclude(
                grn_number__in=grn_document_numbers_with_journal_entries
            ).select_related(
                'purchase_order__vendor',  # Vendor is now a Contact
                'warehouse'
            ).prefetch_related('items__product')
            
            # Get billable GRN Returns (posted but not credited)
            billable_returns = GoodsReturnNote.objects.filter(
                status='POSTED'
            ).exclude(
                grn_return_number__in=grn_return_document_numbers_with_journal_entries
            ).select_related(
                'vendor',  # Now directly a Contact
                'warehouse',
                'original_grn__purchase_order'
            ).prefetch_related('items__product')
            
            # Transform GRNs to billable items
            billable_items = []
            
            # Add GRNs
            for grn in billable_grns:
                vendor_info = get_vendor_info(grn.purchase_order.vendor)  # Vendor is now a Contact
                
                items = []
                for item in grn.items.all():
                    if item.quantity_received > 0 and item.condition == 'GOOD':
                        tax_rate = self._get_po_line_tax_rate(grn, item)
                        tax_amount = self._get_po_line_tax_amount(grn, item)
                        taxable = self._get_po_line_taxable(grn, item)
                        
                        items.append({
                            'product_name': item.product.name if item.product else 'Unknown Product',
                            'product_sku': item.product.sku if item.product else '',
                            'quantity_received': float(item.quantity_received),
                            'unit_cost': float(item.unit_cost),
                            'total_cost': float(item.total_cost),
                            'tax_rate': float(tax_rate),
                            'tax_amount': float(tax_amount),
                            'taxable': taxable
                        })
                
                if items:  # Only add GRN if it has valid items
                    billable_items.append({
                        'type': 'GRN',
                        'id': grn.grn_id,
                        'document_number': grn.grn_number,
                        'document_date': grn.receipt_date,
                        'vendor_name': vendor_info['vendor_name'],
                        'po_number': grn.purchase_order.po_number,
                        'total_value': float(grn.total_value),
                        'warehouse': grn.warehouse.name,
                        'items_count': len(items),
                        'items': items
                    })
            
            # Add GRN Returns
            for grn_return in billable_returns:
                vendor_info = get_vendor_info(grn_return.vendor)  # Now directly a Contact
                
                items = []
                for item in grn_return.items.all():
                    if item.quantity_returned > 0:
                        tax_rate = self._get_po_line_tax_rate_for_return(grn_return, item)
                        tax_amount = self._get_po_line_tax_amount_for_return(grn_return, item)
                        taxable = self._get_po_line_taxable_for_return(grn_return, item)
                        
                        items.append({
                            'product_name': item.product.name,
                            'product_sku': item.product.sku,
                            'quantity_returned': float(item.quantity_returned),
                            'unit_cost': float(item.unit_cost),
                            'return_value': float(item.return_value),
                            'tax_rate': float(tax_rate),
                            'tax_amount': float(tax_amount),
                            'taxable': taxable,
                            'condition': item.get_return_reason_display(),
                            'return_reason': item.return_reason
                        })
                
                if items:  # Only add return if it has items
                    billable_items.append({
                        'type': 'GRN_RETURN',
                        'id': grn_return.grn_return_id,
                        'document_number': grn_return.grn_return_number,
                        'document_date': grn_return.return_date,
                        'vendor_name': vendor_info['vendor_name'],
                        'po_number': grn_return.original_grn.purchase_order.po_number,
                        'total_value': float(grn_return.total_value),
                        'warehouse': grn_return.warehouse.name,
                        'items_count': len(items),
                        'items': items
                    })
            
            return Response({
                'billable_items': billable_items
            })
            
        except Exception as e:
            import traceback
            print("Error in billable_receipts:", str(e))
            print(traceback.format_exc())
            return Response(
                {'error': f'Failed to load billable items: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _get_po_line_tax_rate(self, grn, grn_item):
        """Get tax rate from the corresponding Purchase Order line item"""
        try:
            po = grn.purchase_order
            # Find the matching PO line item by product
            if grn_item.product:
                po_line_item = po.line_items.filter(product=grn_item.product).first()
                if po_line_item:
                    return po_line_item.tax_rate
        except Exception:
            pass
        return 0.0
    
    def _get_po_line_tax_amount(self, grn, grn_item):
        """Get tax amount from the corresponding Purchase Order line item"""
        try:
            po = grn.purchase_order
            # Find the matching PO line item by product
            if grn_item.product:
                po_line_item = po.line_items.filter(product=grn_item.product).first()
                if po_line_item:
                    # Calculate proportional tax amount based on received quantity
                    if po_line_item.quantity > 0:
                        received_ratio = grn_item.quantity_received / po_line_item.quantity
                        return po_line_item.tax_amount * received_ratio
                    return po_line_item.tax_amount
        except Exception:
            pass
        return 0.0
    
    def _get_po_line_taxable(self, grn, grn_item):
        """Get taxable flag from the corresponding Purchase Order line item"""
        try:
            po = grn.purchase_order
            # Find the matching PO line item by product
            if grn_item.product:
                po_line_item = po.line_items.filter(product=grn_item.product).first()
                if po_line_item:
                    return po_line_item.taxable
        except Exception:
            pass
        return False

    def _get_po_line_tax_rate_for_return(self, grn_return, grn_return_item):
        """Get tax rate from the corresponding Purchase Order line item for return"""
        try:
            original_po = grn_return.original_grn.purchase_order
            # Find the matching PO line item by product
            if grn_return_item.product:
                po_line_item = original_po.line_items.filter(product=grn_return_item.product).first()
                if po_line_item:
                    return po_line_item.tax_rate
        except Exception:
            pass
        return 0.0
    
    def _get_po_line_tax_amount_for_return(self, grn_return, grn_return_item):
        """Get tax amount from the corresponding Purchase Order line item for return"""
        try:
            original_po = grn_return.original_grn.purchase_order
            # Find the matching PO line item by product
            if grn_return_item.product:
                po_line_item = original_po.line_items.filter(product=grn_return_item.product).first()
                if po_line_item:
                    # Calculate proportional tax amount based on returned quantity
                    if po_line_item.quantity > 0:
                        returned_ratio = grn_return_item.quantity_returned / po_line_item.quantity
                        return po_line_item.tax_amount * returned_ratio
                    return po_line_item.tax_amount
        except Exception:
            pass
        return 0.0
    
    def _get_po_line_taxable_for_return(self, grn_return, grn_return_item):
        """Get taxable flag from the corresponding Purchase Order line item for return"""
        try:
            original_po = grn_return.original_grn.purchase_order
            # Find the matching PO line item by product
            if grn_return_item.product:
                po_line_item = original_po.line_items.filter(product=grn_return_item.product).first()
                if po_line_item:
                    return po_line_item.taxable
        except Exception:
            pass
        return False


class PaymentTermViewSet(viewsets.ModelViewSet):
    """ViewSet for managing payment terms (shared with sales module)"""
    queryset = PaymentTerm.objects.filter(is_active=True)
    serializer_class = PaymentTermSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'days', 'created_at']
    ordering = ['days', 'name']

    @action(detail=False, methods=['get'])
    def default(self, request):
        """Get the default payment term"""
        try:
            default_term = PaymentTerm.objects.get(is_default=True, is_active=True)
            serializer = self.get_serializer(default_term)
            return Response(serializer.data)
        except PaymentTerm.DoesNotExist:
            return Response({'error': 'No default payment term found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """Set this payment term as default"""
        payment_term = self.get_object()
        
        # Remove default from all other terms
        PaymentTerm.objects.filter(is_default=True).update(is_default=False)
        
        # Set this term as default
        payment_term.is_default = True
        payment_term.save()
        
        serializer = self.get_serializer(payment_term)
        return Response(serializer.data)


class VendorBillViewSet(viewsets.ModelViewSet):
    """ViewSet for managing vendor bills"""
    queryset = VendorBill.objects.all()
    serializer_class = VendorBillSerializer
    permission_classes = [IsAuthenticated]

    def create(self, request, *args, **kwargs):
        """Custom create method to handle vendor bill creation"""
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # Save the vendor bill
            vendor_bill = serializer.save()

            # Return the created vendor bill
            response_serializer = self.get_serializer(vendor_bill)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        """Custom destroy method to prevent deletion of posted bills"""
        try:
            vendor_bill = self.get_object()

            if vendor_bill.status == 'posted':
                return Response(
                    {'error': 'Posted vendor bills cannot be deleted. Only draft bills can be deleted.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Allow deletion for draft bills
            vendor_bill.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def get_queryset(self):
        """Filter queryset based on query parameters"""
        queryset = super().get_queryset()

        # Date range filtering
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')

        if date_from:
            queryset = queryset.filter(bill_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(bill_date__lte=date_to)

        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get vendor bill statistics"""
        from django.db.models import Q, Sum, Count
        from django.utils import timezone

        queryset = self.get_queryset()

        # Basic counts
        total_bills = queryset.count()
        paid_count = queryset.filter(status='paid').count()
        overdue_count = queryset.filter(status='overdue').count()
        draft_count = queryset.filter(status='draft').count()

        # Financial totals
        total_payables = queryset.aggregate(
            total=Sum('total_amount')
        )['total'] or 0

        outstanding_amount = queryset.filter(
            status__in=['pending', 'overdue']
        ).aggregate(
            total=Sum('balance_due')
        )['total'] or 0

        return Response({
            'total_bills': total_bills,
            'total_payables': float(total_payables),
            'outstanding_amount': float(outstanding_amount),
            'overdue_count': overdue_count,
            'draft_count': draft_count,
            'paid_count': paid_count,
        })

    @action(detail=True, methods=['post'])
    def mark_paid(self, request, pk=None):
        """Mark vendor bill as paid"""
        vendor_bill = self.get_object()
        payment_data = request.data

        payment_amount = float(payment_data.get('payment_amount', vendor_bill.balance_due))

        # Update payment information
        vendor_bill.amount_paid += payment_amount
        vendor_bill.save()  # This will recalculate balance_due and update status

        return Response({'status': 'Vendor bill marked as paid'})

    @action(detail=True, methods=['post'])
    def duplicate(self, request, pk=None):
        """Duplicate vendor bill"""
        original_bill = self.get_object()

        # Create new bill with same data but new number
        new_bill = VendorBill.objects.create(
            vendor=original_bill.vendor,
            bill_date=timezone.now().date(),
            due_date=original_bill.due_date,
            status='draft',
            reference_number=original_bill.reference_number,
            notes=original_bill.notes,
            terms=original_bill.terms,
        )

        # Copy line items
        for item in original_bill.items.all():
            VendorBillItem.objects.create(
                vendor_bill=new_bill,
                product=item.product,
                description=item.description,
                quantity=item.quantity,
                unit_cost=item.unit_cost,
                tax_rate=item.tax_rate,
                line_order=item.line_order,
            )

        # Calculate totals
        new_bill.calculate_totals()
        new_bill.save()

        serializer = self.get_serializer(new_bill)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get vendor bills summary for dashboard"""
        from django.db.models import Sum, Count

        queryset = self.get_queryset()

        summary = queryset.aggregate(
            total_bills=Count('id'),
            total_amount=Sum('total_amount'),
            paid_amount=Sum('amount_paid'),
            outstanding_amount=Sum('balance_due'),
        )

        # Calculate overdue amount
        overdue_amount = queryset.filter(
            status='overdue'
        ).aggregate(
            total=Sum('balance_due')
        )['total'] or 0

        return Response({
            'total_bills': summary['total_bills'] or 0,
            'total_amount': float(summary['total_amount'] or 0),
            'paid_amount': float(summary['paid_amount'] or 0),
            'outstanding_amount': float(summary['outstanding_amount'] or 0),
            'overdue_amount': float(overdue_amount),
        })

    @action(detail=False, methods=['post'])
    def create_from_grn(self, request):
        """Create vendor bill from GRN (Goods Receipt Note)"""
        grn_id = request.data.get('grn_id')
        if not grn_id:
            return Response({'error': 'GRN ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            from inventory.models import GoodsReceiptNote
            grn = GoodsReceiptNote.objects.get(id=grn_id)

            # Create vendor bill from GRN
            vendor_bill = VendorBill.objects.create(
                vendor=grn.vendor,
                grn=grn,
                bill_date=timezone.now().date(),
                due_date=timezone.now().date() + timedelta(days=30),
                status='draft',
                reference_number=f"GRN-{grn.grn_number}",
                notes=f"Bill created from GRN {grn.grn_number}"
            )

            # Copy line items from GRN
            for grn_item in grn.line_items.all():
                VendorBillItem.objects.create(
                    vendor_bill=vendor_bill,
                    product=grn_item.product,
                    item_description=grn_item.description,
                    quantity=grn_item.quantity_received,
                    unit_price=grn_item.unit_cost,
                    account_code=getattr(grn_item.product, 'expense_account_code', '5010-COGS')
                )

            # Calculate totals
            vendor_bill.calculate_totals()
            vendor_bill.save()

            serializer = self.get_serializer(vendor_bill)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def create_from_po(self, request):
        """Create vendor bill from Purchase Order (for services)"""
        po_id = request.data.get('po_id')
        if not po_id:
            return Response({'error': 'PO ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            po = PurchaseOrder.objects.get(id=po_id)
            print(f"Found PO: {po.po_number}")

            # Prepare vendor bill data using serializer-friendly format
            bill_data = {
                'vendor': po.vendor.id,
                'bill_date': timezone.now().date(),
                'due_date': timezone.now().date() + timedelta(days=30),
                'status': 'draft',
                'reference_number': f"PO-{po.po_number}",
                'notes': f"Bill created from PO {po.po_number}",
                'line_items': []
            }

            # Copy line items from PO with proper data format
            for po_item in po.line_items.all():
                line_item_data = {
                    'product': po_item.product.id if po_item.product else None,
                    'item_description': po_item.description,
                    'quantity': po_item.quantity,
                    'unit_price': po_item.unit_price,
                    'tax_rate': po_item.tax_rate or 0,
                    'account_code': getattr(po_item.product, 'expense_account_code', '5010-COGS') if po_item.product else '5010-COGS'
                }
                bill_data['line_items'].append(line_item_data)

            print(f"Prepared bill data with {len(bill_data['line_items'])} line items")

            # Create vendor bill using serializer
            serializer = self.get_serializer(data=bill_data)
            if serializer.is_valid():
                vendor_bill = serializer.save()
                print(f"Created vendor bill: {vendor_bill.bill_number}")
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            else:
                print(f"Serializer validation errors: {serializer.errors}")
                return Response({'error': 'Validation failed', 'details': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            import traceback
            print(f"Error in create_from_po: {e}")
            print(f"Traceback: {traceback.format_exc()}")
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def create_from_return_note(self, request):
        """Create vendor bill from Goods Return Note"""
        return_note_id = request.data.get('return_note_id')
        if not return_note_id:
            return Response({'error': 'Return Note ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            from inventory.models import GoodsReturnNote
            return_note = GoodsReturnNote.objects.get(id=return_note_id)

            # Create vendor bill from return note (usually for return processing fees)
            vendor_bill = VendorBill.objects.create(
                vendor=return_note.vendor,
                goods_return_note=return_note,
                bill_date=timezone.now().date(),
                due_date=timezone.now().date() + timedelta(days=30),
                status='draft',
                reference_number=f"RET-{return_note.return_number}",
                notes=f"Bill created from Return Note {return_note.return_number}"
            )

            # Copy line items from return note
            for return_item in return_note.line_items.all():
                VendorBillItem.objects.create(
                    vendor_bill=vendor_bill,
                    product=return_item.product,
                    item_description=return_item.description,
                    quantity=return_item.quantity_returned,
                    unit_price=return_item.unit_cost,
                    account_code=getattr(return_item.product, 'expense_account_code', '5010-COGS')
                )

            # Calculate totals
            vendor_bill.calculate_totals()
            vendor_bill.save()

            serializer = self.get_serializer(vendor_bill)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

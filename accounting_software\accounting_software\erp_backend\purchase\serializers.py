from rest_framework import serializers
from .models import <PERSON>ur<PERSON><PERSON><PERSON><PERSON>, PurchaseOrderLineItem, VendorBill, VendorBillItem
from contacts.models import Contact, Vendor


class VendorSerializer(serializers.ModelSerializer):
    """Serializer for Vendor model with frontend-compatible fields"""
    # Explicitly include id field (which is the contact_id since contact is the primary key)
    id = serializers.IntegerField(source='contact.id', read_only=True)
    
    # Add fields expected by frontend
    display_name = serializers.CharField(source='contact.name', read_only=True)
    vendor_id = serializers.CharField(source='vendor_code', read_only=True)
    email = serializers.CharField(source='contact.email', read_only=True)
    phone = serializers.CharField(source='contact.phone', read_only=True)
    created_at = serializers.CharField(source='contact.created_at', read_only=True)
    updated_at = serializers.CharField(source='contact.updated_at', read_only=True)
    
    # Add default values for fields that don't exist in Contact/Vendor model but frontend expects
    vendor_type = serializers.SerializerMethodField()
    current_balance = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    currency = serializers.SerializerMethodField()
    
    class Meta:
        model = Vendor
        fields = '__all__'
        
    def get_vendor_type(self, obj):
        # Map vendor_category to vendor_type expected by frontend
        category_to_type = {
            'Supplier': 'supplier',
            'Contractor': 'contractor',
            'Service': 'business',
        }
        return category_to_type.get(obj.vendor_category, 'business')
    
    def get_current_balance(self, obj):
        # Default to 0 for now, can be calculated from transactions later
        return 0.0
    
    def get_status(self, obj):
        # Default to active for now
        return 'active'
    
    def get_currency(self, obj):
        # Use company default currency
        return 'USD'


class PurchaseOrderLineItemSerializer(serializers.ModelSerializer):
    """Serializer for Purchase Order Line Items"""
    
    class Meta:
        model = PurchaseOrderLineItem
        fields = '__all__'
        read_only_fields = ('purchase_order', 'line_total', 'tax_amount', 'quantity_pending')


class PurchaseOrderSerializer(serializers.ModelSerializer):
    """Serializer for Purchase Order model"""
    line_items = PurchaseOrderLineItemSerializer(many=True, required=False)
    vendor_name = serializers.CharField(source='vendor.name', read_only=True)
    
    class Meta:
        model = PurchaseOrder
        fields = '__all__'
        read_only_fields = ('po_id', 'po_number', 'subtotal', 'total_amount', 'balance_due', 'created_at', 'updated_at', 'created_by')
    
    def create(self, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        validated_data['created_by'] = self.context['request'].user
        
        purchase_order = PurchaseOrder.objects.create(**validated_data)
        
        # Create line items
        for line_item_data in line_items_data:
            PurchaseOrderLineItem.objects.create(
                purchase_order=purchase_order,
                **line_item_data
            )
        
        # Recalculate totals after creating line items
        purchase_order.calculate_totals()
        purchase_order.save()
        
        return purchase_order
    
    def update(self, instance, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        
        # Update purchase order fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update line items
        if line_items_data:
            # Delete existing line items
            instance.line_items.all().delete()
            
            # Create new line items
            for line_item_data in line_items_data:
                PurchaseOrderLineItem.objects.create(
                    purchase_order=instance,
                    **line_item_data
                )
            
            # Recalculate totals after updating line items
            instance.calculate_totals()
            instance.save()
        
        return instance


class VendorBillVendorSerializer(serializers.ModelSerializer):
    """Serializer for vendor details in vendor bills"""
    id = serializers.IntegerField(read_only=True)
    display_name = serializers.CharField(source='name', read_only=True)
    email = serializers.CharField(read_only=True)
    phone = serializers.CharField(read_only=True)
    billing_address = serializers.SerializerMethodField()
    payment_terms = serializers.SerializerMethodField()

    class Meta:
        model = Contact
        fields = ['id', 'display_name', 'email', 'phone', 'billing_address', 'payment_terms']

    def get_billing_address(self, obj):
        # Return the address field from Contact model
        return obj.address or ''

    def get_payment_terms(self, obj):
        # Get payment terms from vendor profile if exists
        try:
            vendor = obj.vendor
            return vendor.payment_terms if vendor else ''
        except:
            return ''


class VendorBillItemSerializer(serializers.ModelSerializer):
    """Serializer for vendor bill line items with GL integration"""
    product_name = serializers.CharField(source='product.name', read_only=True)

    # Override fields to make them optional and handle validation properly
    product = serializers.IntegerField(required=False, allow_null=True)
    item_description = serializers.CharField(required=False, allow_blank=True)
    quantity = serializers.DecimalField(max_digits=10, decimal_places=2, default=1.00, required=False)
    unit_price = serializers.DecimalField(max_digits=15, decimal_places=2, default=0.00, required=False)
    tax_rate = serializers.DecimalField(max_digits=5, decimal_places=2, default=0.00, required=False)
    account_code = serializers.CharField(required=False, allow_blank=True)
    line_order = serializers.IntegerField(default=1, required=False)

    class Meta:
        model = VendorBillItem
        fields = [
            'id', 'product', 'product_name', 'item_description', 'quantity',
            'unit_price', 'line_total', 'tax_rate', 'tax_amount', 'account_code', 'line_order'
        ]
        read_only_fields = ['line_total', 'tax_amount']




class VendorBillSerializer(serializers.ModelSerializer):
    """Serializer for vendor bills with comprehensive ERP features"""
    vendor_details = VendorBillVendorSerializer(source='vendor', read_only=True)
    line_items = VendorBillItemSerializer(many=True, required=False)

    def validate_line_items(self, value):
        """Simple validation for line items"""
        if not value:
            return value

        # Just return the value without complex processing
        return value

    # Document link details
    purchase_order_details = serializers.SerializerMethodField()
    grn_details = serializers.SerializerMethodField()
    goods_return_note_details = serializers.SerializerMethodField()

    class Meta:
        model = VendorBill
        fields = [
            'id', 'bill_number', 'vendor', 'vendor_details', 'bill_date', 'due_date',
            'purchase_order', 'purchase_order_details', 'grn', 'grn_details',
            'goods_return_note', 'goods_return_note_details',
            'status', 'payment_terms', 'line_items', 'subtotal', 'tax_amount', 'total_amount',
            'amount_paid', 'balance_due', 'reference_number', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['bill_number', 'subtotal', 'tax_amount', 'total_amount', 'balance_due']

    def get_purchase_order_details(self, obj):
        if obj.purchase_order:
            return {
                'po_number': obj.purchase_order.po_number,
                'po_date': obj.purchase_order.po_date,
                'total_amount': obj.purchase_order.total_amount
            }
        return None

    def get_grn_details(self, obj):
        if obj.grn:
            return {
                'grn_number': obj.grn.grn_number,
                'grn_date': obj.grn.grn_date,
                'total_amount': getattr(obj.grn, 'total_amount', 0)
            }
        return None

    def get_goods_return_note_details(self, obj):
        if obj.goods_return_note:
            return {
                'return_number': obj.goods_return_note.return_number,
                'return_date': obj.goods_return_note.return_date,
                'total_amount': getattr(obj.goods_return_note, 'total_amount', 0)
            }
        return None

    def create(self, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        vendor_bill = VendorBill.objects.create(**validated_data)

        # Create line items with minimal processing
        for item_data in line_items_data:
            # Skip completely empty line items
            if not item_data or not any(item_data.values()):
                continue

            # Set defaults
            item_data.setdefault('quantity', 1.00)
            item_data.setdefault('unit_price', 0.00)
            item_data.setdefault('tax_rate', 0.00)
            item_data.setdefault('line_order', 1)
            item_data.setdefault('item_description', 'Line Item')

            # Handle product field - convert ID to instance if needed
            if item_data.get('product'):
                try:
                    from sales.models import Product
                    product_obj = Product.objects.get(id=item_data['product'])
                    item_data['product'] = product_obj
                except (Product.DoesNotExist, ValueError, TypeError):
                    item_data['product'] = None

            VendorBillItem.objects.create(vendor_bill=vendor_bill, **item_data)

        # Calculate totals
        vendor_bill.calculate_totals()
        vendor_bill.save()

        return vendor_bill

    def update(self, instance, validated_data):
        line_items_data = validated_data.pop('line_items', None)

        # Update vendor bill fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update line items if provided
        if line_items_data is not None:
            # Delete existing line items
            instance.line_items.all().delete()

            # Create new line items
            for item_data in line_items_data:
                VendorBillItem.objects.create(vendor_bill=instance, **item_data)

            # Recalculate totals after updating line items
            instance.calculate_totals()
            instance.save()

        return instance